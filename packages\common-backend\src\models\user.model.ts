import { Address as AddressModel, User as UserModel } from '@linqpal/models'
import mongoose, { Schema } from 'mongoose'
import './role.model'
import createSchema from '../helpers/createSchema'
import { wirePhoneValidator } from '../helpers/phoneValidator'
import { IUser } from './types'

const addressSchema = createSchema(AddressModel)
const AddressSchema = new Schema(addressSchema, { timestamps: false })

const UserSchema = createSchema(UserModel)
UserSchema.sub.unique = true
UserSchema.firebaseId.unique = true
UserSchema.login.unique = true
UserSchema.email.unique = true
UserSchema.phone.index = true
UserSchema.login.index = true
UserSchema.hubspotId = { type: String }
UserSchema.hubspotLastSyncDate = { type: Date }
UserSchema.addresses.type = [AddressSchema]
UserSchema.isGuest = { type: Boolean }
UserSchema.settings = {
  ip: String,
  ip_based_city: String,
  ip_based_state: String,
  ua: String,
  conversion: Object,
  opt_for_marketing_messages: Boolean,
  startLOCAfterSignUp: Boolean,
}
UserSchema.guestInfo = {
  phone: String,
  email: String,
}

const userSchema = new Schema<IUser>(UserSchema, { timestamps: true })
wirePhoneValidator(userSchema, 'phone')

export const User = mongoose.model<IUser>('User', userSchema)
