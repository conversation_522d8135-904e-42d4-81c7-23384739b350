import { encryptValidate } from './cryptValidator'
import { validatePhone } from './phoneValidator'
import { Document, Model, Query, Schema } from 'mongoose'
import { KeysOfType } from './helperTypes'

type ValidatorTypes = 'crypt' | 'phone'

type RecordKeys<T> = KeysOfType<T, Record<string, any>>

export function wireChildValidator<T extends Document>(
  schema: Schema<any>,
  child: RecordKeys<T>,
  column: string,
  type: ValidatorTypes,
) {
  schema.pre('validate', preValidate<T>(child, column, type))
  schema.pre('updateOne', preUpdateOne<T>(child, column, type))
}

function preValidate<T extends Document>(
  child: RecordKeys<T>,
  column: string,
  type: ValidatorTypes,
) {
  return async function (this: T, next: () => void) {
    const childRecord = this[child] as Record<string, any>
    if (childRecord && type === 'crypt') {
      childRecord[column] = await encryptValidate(childRecord, column)
    }
    if (childRecord && type === 'phone') {
      childRecord[column] = validatePhone(childRecord[column])
    }
    next()
  }
}

function preUpdateOne<TDoc>(
  child: string & keyof TDoc,
  column: string,
  type: ValidatorTypes,
) {
  return async function (this: Query<TDoc, TDoc>, next: () => void) {
    const data = this.getUpdate() as TDoc
    if (data) {
      const val = (this as any)[child]
      const childRecord = data[child] as Record<string, any>
      if (val && type === 'crypt') {
        childRecord[column] = await encryptValidate(childRecord, column)
      }
      if (val && type === 'phone') {
        childRecord[column] = validatePhone(childRecord[column])
      }
      this.setUpdate(data)
    }

    next()
  }
}
