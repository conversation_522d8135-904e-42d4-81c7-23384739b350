import * as libPhone from 'libphonenumber-js'
import { Document, Model, Query, Schema } from 'mongoose'
import { KeysOfType } from './helperTypes'

type StringKeys<T> = KeysOfType<T, string | null | undefined>

export function wirePhoneValidator<T extends Document>(
  schema: Schema<any>,
  column: StringKeys<T>,
) {
  schema.pre('validate', preValidate<T>(column))
  schema.pre('updateOne', preUpdateOne<T>(column))
}

export function preValidate<T extends Document>(column: StringKeys<T>) {
  return function (this: T, next: () => void) {
    this[column] = validatePhone(this[column] as unknown as string) as any
    next()
  }
}

export function preUpdateOne<T extends Document>(column: StringKeys<T>) {
  return function (this: Query<T, T>, next: () => void) {
    const data = this.getUpdate() as Partial<T>
    if (data) {
      if (column in data) {
        data[column] = validatePhone(data[column] as unknown as string) as any
        this.setUpdate(data)
      }
    }

    next()
  }
}

export function validatePhone(phone: string) {
  try {
    const _phone = libPhone.parse(phone, { defaultCountry: 'US' })
    return libPhone.formatNumber(_phone, 'E.164')
  } catch (e) {
    return undefined
  }
}
